FROM node:22-alpine AS builder

WORKDIR /app

# 复制共享模块
COPY shared/ ./shared/

# 复制服务注册中心代码
COPY service-registry/package*.json ./service-registry/
WORKDIR /app/service-registry
RUN npm install

# 复制服务注册中心源代码
COPY service-registry/ ./

RUN npm run build

FROM node:22-alpine

# 安装 wget 和 curl 用于健康检查
RUN apk add --no-cache wget curl

WORKDIR /app

COPY --from=builder /app/service-registry/package*.json ./
COPY --from=builder /app/service-registry/dist ./dist

RUN npm install --only=production

EXPOSE 3010 4010

CMD ["node", "dist/service-registry/src/main.js"]
